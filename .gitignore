# Maven
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Gradle
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

### IntelliJ IDEA ###
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/
*.code-workspace

### Mac OS ###
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

### Windows ###
*.tmp
*.temp
*.log
*.swp
*.swo
*~

### Java ###
*.class
*.ctxt
.mtj.tmp/
hs_err_pid*
replay_pid*

### Spring Boot ###
spring-boot-*.log
application-*.properties
application-*.yml
!application.properties
!application.yml
!application-example.properties
!application-example.yml

### Database ###
*.db
*.sqlite
*.sqlite3
*.h2.db

### Logs ###
logs/
*.log
log/

### Temporary files ###
tmp/
temp/
*.tmp
*.temp
*.bak
*.backup
*.orig

### Environment variables ###
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

### Node.js (if using frontend) ###
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

### Docker ###
.dockerignore
docker-compose.override.yml

### Security ###
*.key
*.pem
*.p12
*.jks
*.keystore
*.truststore

### IDE specific ###
.metadata
.recommenders
.loadpath
.externalToolBuilders/
.buildpath
.target
.tern-project
.texlipse
.springBeans
.recommenders/

### Coverage reports ###
.nyc_output
coverage/
*.lcov
jacoco.xml
*.exec

### Test results ###
test-results/
junit.xml
TEST-*.xml