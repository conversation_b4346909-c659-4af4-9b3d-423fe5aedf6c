package cn.sunxiansheng.springai.util;

import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;

/**
 * <h3>通用响应封装</h3>
 *
 * <p>所有 REST/SSE/WebSocket 等接口统一返回此对象，前端只需解析
 * {@code code}、{@code message}、{@code data} 三个字段即可。</p>
 *
 * <p><strong>字段含义</strong></p>
 * <ul>
 *   <li><b>code</b>：业务状态码，约定 0 为成功，其余为各种业务错误。</li>
 *   <li><b>message</b>：对前端或日志友好的提示信息。</li>
 *   <li><b>data</b>：真正的业务负载，可为任意类型（使用泛型参数 <code>&lt;T&gt;</code>）。</li>
 *   <li><b>timestamp</b>：服务器返回时间戳（毫秒），便于前端排查接口时差。</li>
 * </ul>
 *
 * @param <T> data 字段的数据类型
 */
public class Response<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务状态码：0 表示成功，其它值自行约定
     */
    private int code;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 业务数据
     */
    private T data;

    /**
     * 服务器时间戳（毫秒）
     */
    private long timestamp;

    /* ---------- 构造器 ---------- */

    private Response() {
        // 使用工厂方法创建，构造器私有化
    }

    private Response(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = Instant.now().toEpochMilli();
    }

    /* ---------- 静态工厂方法 ---------- */

    /**
     * 成功且无数据返回
     */
    public static <T> Response<T> success() {
        return new Response<>(0, "success", null);
    }

    /**
     * 成功并携带数据
     */
    public static <T> Response<T> success(T data) {
        return new Response<>(0, "success", data);
    }

    /**
     * 成功并自定义提示信息
     */
    public static <T> Response<T> success(String message, T data) {
        return new Response<>(0, message, data);
    }

    /**
     * 失败，默认业务错误码为 -1
     */
    public static <T> Response<T> failure(String message) {
        return new Response<>(-1, message, null);
    }

    /**
     * 失败，带自定义业务错误码
     */
    public static <T> Response<T> failure(int code, String message) {
        return new Response<>(code, message, null);
    }

    /* ---------- 链式 set 方法（可选） ---------- */

    public Response<T> withCode(int code) {
        this.code = code;
        return this;
    }

    public Response<T> withMessage(String message) {
        this.message = message;
        return this;
    }

    public Response<T> withData(T data) {
        this.data = data;
        return this;
    }

    /* ---------- Getter / Setter ---------- */

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    /* ---------- toString ---------- */

    @Override
    public String toString() {
        return "ApiResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                '}';
    }
}
