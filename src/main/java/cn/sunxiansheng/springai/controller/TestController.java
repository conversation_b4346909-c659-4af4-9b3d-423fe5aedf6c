package cn.sunxiansheng.springai.controller;

import cn.sunxiansheng.springai.util.Response;
import jakarta.annotation.Resource;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@RestController
public class TestController {

    /**
     * 获取DeepSeek聊天客户端
     */
    @Resource
    private ChatClient chatClient;

    /**
     * A test endpoint.
     *
     * @return A sample response.
     */
    @RequestMapping("/test")
    public String test() {
        return "This is a test response from TestController";
    }

    @RequestMapping("/ask")
    public String ask() {
        return chatClient.prompt("你好").call().content();
    }

    /**
     * 流式输出
     */
    @GetMapping(value = "/askStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<Response<String>> askStream() {
        return chatClient.prompt()
                .user("Tell me a joke")
                .stream()
                .content()
                .map(Response::success); // 把每条数据包装成响应对象
    }
}