package cn.sunxiansheng.springai.controller;

import jakarta.annotation.Resource;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {

    /**
     * 获取OpenAIChatClient
     */
    @Resource
    private ChatClient chatClient;

    /**
     * A test endpoint.
     *
     * @return A sample response.
     */
    @RequestMapping("/test")
    public String test() {
        return "This is a test response from TestController";
    }

    @RequestMapping("/ask")
    public String ask() {
        return chatClient.prompt("你好").call().content();
    }
}